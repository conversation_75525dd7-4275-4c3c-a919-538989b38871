-- Users policies
CREATE POLICY "Users can view all users" ON public.users
  FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Groups policies
CREATE POLICY "Users can view public groups and groups they're members of" ON public.groups
  FOR SELECT USING (
    NOT is_private OR 
    id IN (
      SELECT group_id FROM public.group_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create groups" ON public.groups
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Group owners can update their groups" ON public.groups
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Group owners can delete their groups" ON public.groups
  FOR DELETE USING (auth.uid() = owner_id);

-- Group members policies
CREATE POLICY "Users can view group members" ON public.group_members
  FOR SELECT USING (
    -- User is a member of this specific group (direct check without subquery)
    user_id = auth.uid() OR
    -- OR the group is public (check groups table directly)
    group_id IN (
      SELECT id FROM public.groups
      WHERE is_private = false
    ) OR
    -- OR user is a member of this group (using EXISTS to avoid recursion)
    EXISTS (
      SELECT 1 FROM public.group_members gm2
      WHERE gm2.group_id = group_members.group_id
      AND gm2.user_id = auth.uid()
    )
  );

CREATE POLICY "Group owners can add members" ON public.group_members
  FOR INSERT WITH CHECK (
    group_id IN (
      SELECT id FROM public.groups
      WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "Group owners and users can remove members" ON public.group_members
  FOR DELETE USING (
    -- Group owner can remove anyone
    group_id IN (
      SELECT id FROM public.groups
      WHERE owner_id = auth.uid()
    ) OR
    -- Users can remove themselves
    user_id = auth.uid()
  );

-- Media assets policies
CREATE POLICY "Users can view public media and their own media" ON public.media_assets
  FOR SELECT USING (
    is_public OR
    user_id = auth.uid() OR
    (group_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = media_assets.group_id
      AND gm.user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can create their own media" ON public.media_assets
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own media" ON public.media_assets
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own media" ON public.media_assets
  FOR DELETE USING (auth.uid() = user_id);

-- Friendships policies
CREATE POLICY "Users can view their own friendships" ON public.friendships
  FOR SELECT USING (
    requester_id = auth.uid() OR 
    addressee_id = auth.uid()
  );

CREATE POLICY "Users can create friendship requests" ON public.friendships
  FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Users can update friendships they're involved in" ON public.friendships
  FOR UPDATE USING (
    requester_id = auth.uid() OR 
    addressee_id = auth.uid()
  );

CREATE POLICY "Users can delete friendships they're involved in" ON public.friendships
  FOR DELETE USING (
    requester_id = auth.uid() OR 
    addressee_id = auth.uid()
  );

-- Chat messages policies
CREATE POLICY "Users can view messages in groups they're members of" ON public.chat_messages
  FOR SELECT USING (
    (group_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = chat_messages.group_id
      AND gm.user_id = auth.uid()
    )) OR
    (recipient_id = auth.uid() OR sender_id = auth.uid())
  );

CREATE POLICY "Users can send messages to groups they're members of" ON public.chat_messages
  FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND (
      (group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.group_members gm
        WHERE gm.group_id = chat_messages.group_id
        AND gm.user_id = auth.uid()
      )) OR
      (recipient_id IS NOT NULL AND recipient_id IN (
        SELECT CASE
          WHEN requester_id = auth.uid() THEN addressee_id
          WHEN addressee_id = auth.uid() THEN requester_id
        END
        FROM public.friendships
        WHERE status = 'accepted' AND
        (requester_id = auth.uid() OR addressee_id = auth.uid())
      ))
    )
  );


