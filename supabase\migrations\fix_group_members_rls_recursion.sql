-- Fix infinite recursion in RLS policies
-- This migration removes circular dependencies that cause infinite recursion

-- Drop the problematic group_members policies
DROP POLICY IF EXISTS "Users can view group members of groups they're in" ON public.group_members;
DROP POLICY IF EXISTS "Users can view group members" ON public.group_members;
DROP POLICY IF EXISTS "Group owners and admins can add members" ON public.group_members;
DROP POLICY IF EXISTS "Group owners can add members" ON public.group_members;
DROP POLICY IF EXISTS "Group owners and admins can remove members" ON public.group_members;
DROP POLICY IF EXISTS "Group owners and users can remove members" ON public.group_members;
DROP POLICY IF EXISTS "Group admins can add members" ON public.group_members;
DROP POLICY IF EXISTS "Group admins can remove members" ON public.group_members;

-- Drop problematic media assets policies
DROP POLICY IF EXISTS "Users can view public media and their own media" ON public.media_assets;

-- Drop problematic chat messages policies
DROP POLICY IF EXISTS "Users can view messages in groups they're members of" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to groups they're members of" ON public.chat_messages;

-- Create new policies that avoid circular dependencies

-- GROUP_MEMBERS POLICIES
-- Policy 1: Users can view group members if they are members of the group OR if the group is public
CREATE POLICY "Users can view group members" ON public.group_members
  FOR SELECT USING (
    -- User is a member of this specific group (direct check without subquery)
    user_id = auth.uid() OR
    -- OR the group is public (check groups table directly)
    group_id IN (
      SELECT id FROM public.groups
      WHERE is_private = false
    ) OR
    -- OR user is a member of this group (using EXISTS to avoid recursion)
    EXISTS (
      SELECT 1 FROM public.group_members gm2
      WHERE gm2.group_id = group_members.group_id
      AND gm2.user_id = auth.uid()
    )
  );

-- Policy 2: Group owners can add members (check ownership via groups table)
CREATE POLICY "Group owners can add members" ON public.group_members
  FOR INSERT WITH CHECK (
    group_id IN (
      SELECT id FROM public.groups
      WHERE owner_id = auth.uid()
    )
  );

-- Policy 3: Group owners and the user themselves can remove members
CREATE POLICY "Group owners and users can remove members" ON public.group_members
  FOR DELETE USING (
    -- Group owner can remove anyone
    group_id IN (
      SELECT id FROM public.groups
      WHERE owner_id = auth.uid()
    ) OR
    -- Users can remove themselves
    user_id = auth.uid()
  );

-- MEDIA ASSETS POLICIES
CREATE POLICY "Users can view public media and their own media" ON public.media_assets
  FOR SELECT USING (
    is_public OR
    user_id = auth.uid() OR
    (group_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = media_assets.group_id
      AND gm.user_id = auth.uid()
    ))
  );

-- CHAT MESSAGES POLICIES
CREATE POLICY "Users can view messages in groups they're members of" ON public.chat_messages
  FOR SELECT USING (
    (group_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = chat_messages.group_id
      AND gm.user_id = auth.uid()
    )) OR
    (recipient_id = auth.uid() OR sender_id = auth.uid())
  );

CREATE POLICY "Users can send messages to groups they're members of" ON public.chat_messages
  FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND (
      (group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.group_members gm
        WHERE gm.group_id = chat_messages.group_id
        AND gm.user_id = auth.uid()
      )) OR
      (recipient_id IS NOT NULL AND recipient_id IN (
        SELECT CASE
          WHEN requester_id = auth.uid() THEN addressee_id
          WHEN addressee_id = auth.uid() THEN requester_id
        END
        FROM public.friendships
        WHERE status = 'accepted' AND
        (requester_id = auth.uid() OR addressee_id = auth.uid())
      ))
    )
  );
