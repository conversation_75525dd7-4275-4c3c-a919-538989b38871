-- Fix infinite recursion in group_members RLS policies
-- This migration removes the circular dependency that causes infinite recursion

-- Drop the problematic policies
DROP POLICY IF EXISTS "Users can view group members of groups they're in" ON public.group_members;
DROP POLICY IF EXISTS "Group owners and admins can add members" ON public.group_members;
DROP POLICY IF EXISTS "Group owners and admins can remove members" ON public.group_members;

-- Create new policies that avoid circular dependencies

-- Policy 1: Users can view group members if they are members of the group OR if the group is public
CREATE POLICY "Users can view group members" ON public.group_members
  FOR SELECT USING (
    -- User is a member of this specific group (direct check without subquery)
    user_id = auth.uid() OR
    -- OR the group is public (check groups table directly)
    group_id IN (
      SELECT id FROM public.groups 
      WHERE is_private = false
    ) OR
    -- OR user is a member of this group (using EXISTS to avoid recursion)
    EXISTS (
      SELECT 1 FROM public.group_members gm2
      WHERE gm2.group_id = group_members.group_id 
      AND gm2.user_id = auth.uid()
    )
  );

-- Policy 2: Group owners can add members (check ownership via groups table)
CREATE POLICY "Group owners can add members" ON public.group_members
  FOR INSERT WITH CHECK (
    group_id IN (
      SELECT id FROM public.groups 
      WHERE owner_id = auth.uid()
    )
  );

-- Policy 3: Group owners and the user themselves can remove members
CREATE POLICY "Group owners and users can remove members" ON public.group_members
  FOR DELETE USING (
    -- Group owner can remove anyone
    group_id IN (
      SELECT id FROM public.groups 
      WHERE owner_id = auth.uid()
    ) OR
    -- Users can remove themselves
    user_id = auth.uid()
  );

-- Policy 4: Allow group admins to also add/remove members
CREATE POLICY "Group admins can add members" ON public.group_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = group_members.group_id
      AND gm.user_id = auth.uid()
      AND gm.role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Group admins can remove members" ON public.group_members
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.group_members gm
      WHERE gm.group_id = group_members.group_id
      AND gm.user_id = auth.uid()
      AND gm.role IN ('owner', 'admin')
    ) OR
    -- Users can still remove themselves
    user_id = auth.uid()
  );
